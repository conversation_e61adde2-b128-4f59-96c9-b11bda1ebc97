"""
单位转换工具 - 现代化版本
使用CustomTkinter实现现代化界面设计
支持字节单位转换、时间戳转换、货币汇率转换
"""

import customtkinter as ctk
import tkinter as tk
import time
import datetime
import requests  # 用于获取实时汇率

# 设置CustomTkinter外观
ctk.set_appearance_mode("System")  # 系统模式
ctk.set_default_color_theme("blue")  # 蓝色主题

# 尝试导入BeautifulSoup，如果不可用，记录错误
try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False



class ConverterApp:
    """
    A beautiful application for unit conversions.
    Supports:
    - Byte units (B, KB, MB, GB, TB, PB)
    - Timestamp conversion
    - Currency exchange
    """

    # 现代化颜色系统 - 参考spliced_pictures_qt.py的设计理念
    # 主色调
    PRIMARY_BLUE = "#1a73e8"
    HOVER_BLUE = "#1557b0"
    ACTIVE_BLUE = "#0d47a1"
    LIGHT_BLUE = "#e3f2fd"

    # 背景色系
    BACKGROUND_MAIN = "#f8f9fa"
    BACKGROUND_CARD = "#ffffff"
    BACKGROUND_SECONDARY = "#f1f3f4"

    # 边框和分割线
    BORDER_LIGHT = "#e9ecef"
    BORDER_MEDIUM = "#dee2e6"
    BORDER_FOCUS = "#1a73e8"

    # 文字颜色
    TEXT_PRIMARY = "#2c3e50"
    TEXT_SECONDARY = "#495057"
    TEXT_MUTED = "#6c757d"
    TEXT_WHITE = "#ffffff"
    TEXT_SUCCESS = "#28a745"
    TEXT_ERROR = "#dc3545"

    # 状态色
    SUCCESS_GREEN = "#28a745"
    SUCCESS_LIGHT = "#d4edda"
    ERROR_RED = "#dc3545"
    ERROR_LIGHT = "#f8d7da"
    WARNING_ORANGE = "#ffc107"
    WARNING_LIGHT = "#fff3cd"

    # 禁用状态
    DISABLED_BG = "#e9ecef"
    DISABLED_TEXT = "#adb5bd"
    DISABLED_BORDER = "#dee2e6"


    def __init__(self):
        # 创建CustomTkinter主窗口
        self.root = ctk.CTk()
        self.root.title("🔧 单位转换工具")

        # 设置窗口大小和居中显示
        window_width = 1000
        window_height = 800
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # 计算居中位置
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.root.minsize(900, 700)



        # Unit conversion factors (powers of 1024)
        self.units = {
            "字节 (B)": 0,
            "千字节 (KB)": 1,
            "兆字节 (MB)": 2,
            "千兆字节 (GB)": 3,
            "太字节 (TB)": 4,
            "拍字节 (PB)": 5
        }
        
        # 初始化汇率相关变量
        self.exchange_rate = 7.2  # 默认汇率：1美元 = 7.2人民币
        self.last_rate_update = None  # 上次汇率更新时间

        # 创建主界面
        self.setup_ui()

        # 设置窗口图标
        self.set_window_icon()

    def set_window_icon(self):
        """设置窗口图标"""
        try:
            self.root.iconbitmap("icon.ico")
        except:
            # 如果找不到图标文件，使用默认图标
            pass




    def setup_ui(self):
        """设置现代化的用户界面 - 左右分栏布局"""
        # 主容器
        main_frame = ctk.CTkFrame(self.root, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 左侧设置面板 - 使用新的颜色系统
        self.settings_frame = ctk.CTkFrame(
            main_frame,
            corner_radius=15,
            fg_color=self.BACKGROUND_CARD,
            border_width=1,
            border_color=self.BORDER_LIGHT
        )
        self.settings_frame.pack(side="left", fill="y", padx=(0, 15), pady=0)
        self.settings_frame.configure(width=420)

        # 左侧面板标题 - 改进字体和颜色
        title_label = ctk.CTkLabel(
            self.settings_frame,
            text="🔧 单位转换设置",
            font=ctk.CTkFont(family="Microsoft YaHei", size=22, weight="bold"),
            text_color=self.TEXT_PRIMARY
        )
        title_label.pack(pady=(25, 30))

        # 创建转换类型选择选项卡 - 使用新的颜色系统
        self.tabview = ctk.CTkTabview(
            self.settings_frame,
            width=400,
            height=520,
            segmented_button_fg_color=self.BORDER_LIGHT,
            segmented_button_selected_color=self.PRIMARY_BLUE,
            segmented_button_selected_hover_color=self.HOVER_BLUE,
            segmented_button_unselected_color=self.BACKGROUND_SECONDARY,
            segmented_button_unselected_hover_color=self.BORDER_MEDIUM,
            text_color=self.TEXT_PRIMARY,
            corner_radius=12,
            fg_color=self.BACKGROUND_CARD
        )
        self.tabview.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # 添加三个选项卡 - 改进图标和文字
        self.byte_tab = self.tabview.add("📊 字节转换")
        self.timestamp_tab = self.tabview.add("⏰ 时间戳")
        self.currency_tab = self.tabview.add("💰 货币汇率")

        # 右侧结果展示面板 - 使用新的颜色系统
        self.result_frame = ctk.CTkFrame(
            main_frame,
            corner_radius=15,
            fg_color=self.BACKGROUND_CARD,
            border_width=1,
            border_color=self.BORDER_LIGHT
        )
        self.result_frame.pack(side="right", fill="both", expand=True, padx=(15, 0), pady=0)

        # 右侧面板标题 - 改进字体和颜色
        result_title = ctk.CTkLabel(
            self.result_frame,
            text="📋 转换结果",
            font=ctk.CTkFont(family="Microsoft YaHei", size=22, weight="bold"),
            text_color=self.TEXT_PRIMARY
        )
        result_title.pack(pady=(25, 30))

        # 结果展示区域 - 使用新的颜色系统
        self.result_display = ctk.CTkTextbox(
            self.result_frame,
            font=ctk.CTkFont(family="Microsoft YaHei", size=13),
            corner_radius=12,
            wrap="word",
            fg_color=self.BACKGROUND_SECONDARY,
            text_color=self.TEXT_PRIMARY,
            border_width=1,
            border_color=self.BORDER_LIGHT
        )
        self.result_display.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # 设置各个转换功能的UI
        self.setup_byte_converter_ui()
        self.setup_timestamp_converter_ui()
        self.setup_currency_converter_ui()

        # 底部状态栏
        self.setup_status_bar()

    def setup_status_bar(self):
        """设置现代化状态栏"""
        # 状态栏容器 - 使用新的颜色系统
        status_frame = ctk.CTkFrame(
            self.root,
            height=50,
            corner_radius=12,
            fg_color=self.BACKGROUND_CARD,
            border_width=1,
            border_color=self.BORDER_LIGHT
        )
        status_frame.pack(fill="x", side="bottom", padx=20, pady=(0, 20))

        # 状态文本 - 改进字体和颜色
        self.status_var = tk.StringVar(value="🟢 准备就绪")
        self.status_label = ctk.CTkLabel(
            status_frame,
            textvariable=self.status_var,
            font=ctk.CTkFont(family="Microsoft YaHei", size=13, weight="500"),
            text_color=self.TEXT_SECONDARY
        )
        self.status_label.pack(side="left", padx=20, pady=12)

    def setup_byte_converter_ui(self):
        """设置字节转换UI - 使用现代化设计"""
        # 输入区域标题 - 改进样式
        title_label = ctk.CTkLabel(
            self.byte_tab,
            text="📊 字节单位转换",
            font=ctk.CTkFont(family="Microsoft YaHei", size=18, weight="bold"),
            text_color=self.TEXT_PRIMARY
        )
        title_label.pack(pady=(20, 25))

        # 数值输入框容器
        value_frame = ctk.CTkFrame(self.byte_tab, fg_color="transparent")
        value_frame.pack(fill="x", padx=20, pady=(0, 15))

        # 输入标签 - 改进样式
        ctk.CTkLabel(
            value_frame,
            text="💾 输入数值:",
            font=ctk.CTkFont(family="Microsoft YaHei", size=13, weight="500"),
            text_color=self.TEXT_SECONDARY
        ).pack(anchor="w", padx=20, pady=(15, 8))

        # 数值输入框 - 使用新的颜色系统
        self.byte_value_var = tk.StringVar()
        self.byte_entry = ctk.CTkEntry(
            value_frame,
            textvariable=self.byte_value_var,
            placeholder_text="请输入数值...",
            font=ctk.CTkFont(family="Microsoft YaHei", size=13),
            height=40,
            corner_radius=10,
            fg_color=self.BACKGROUND_CARD,
            border_color=self.BORDER_MEDIUM,
            border_width=2,
            text_color=self.TEXT_PRIMARY
        )
        self.byte_entry.pack(fill="x", padx=20, pady=(0, 20))

        # 单位选择框容器
        unit_frame = ctk.CTkFrame(self.byte_tab, fg_color="transparent")
        unit_frame.pack(fill="x", padx=20, pady=(0, 15))

        # 单位选择标签 - 改进样式
        ctk.CTkLabel(
            unit_frame,
            text="⚖️ 选择单位:",
            font=ctk.CTkFont(family="Microsoft YaHei", size=13, weight="500"),
            text_color=self.TEXT_SECONDARY
        ).pack(anchor="w", padx=20, pady=(15, 8))

        # 单位选择下拉框 - 使用新的颜色系统
        self.byte_unit_var = tk.StringVar()
        self.byte_unit_combo = ctk.CTkComboBox(
            unit_frame,
            variable=self.byte_unit_var,
            values=list(self.units.keys()),
            font=ctk.CTkFont(family="Microsoft YaHei", size=13),
            height=40,
            state="readonly",
            corner_radius=10,
            fg_color=self.BACKGROUND_CARD,
            border_color=self.BORDER_MEDIUM,
            border_width=2,
            button_color=self.PRIMARY_BLUE,
            button_hover_color=self.HOVER_BLUE,
            dropdown_fg_color=self.BACKGROUND_CARD,
            dropdown_hover_color=self.LIGHT_BLUE,
            dropdown_text_color=self.TEXT_PRIMARY,
            text_color=self.TEXT_PRIMARY
        )
        self.byte_unit_combo.set(list(self.units.keys())[0])
        self.byte_unit_combo.pack(fill="x", padx=20, pady=(0, 20))

        # 转换按钮 - 现代化设计
        self.byte_convert_btn = ctk.CTkButton(
            self.byte_tab,
            text="� 开始转换",
            command=self.convert_bytes,
            font=ctk.CTkFont(family="Microsoft YaHei", size=14, weight="600"),
            height=45,
            fg_color=self.PRIMARY_BLUE,
            hover_color=self.HOVER_BLUE,
            text_color=self.TEXT_WHITE,
            corner_radius=12
        )
        self.byte_convert_btn.pack(pady=25)

        # 绑定事件
        self.byte_entry.bind("<Return>", lambda e: self.convert_bytes())
        self.byte_entry.bind("<KeyRelease>", lambda e: self.validate_byte_input())
        self.byte_unit_combo.configure(command=lambda choice: self.convert_bytes())

    def setup_timestamp_converter_ui(self):
        """设置时间戳转换UI - 使用现代化设计"""
        # 输入区域标题 - 改进样式
        title_label = ctk.CTkLabel(
            self.timestamp_tab,
            text="⏰ 时间戳转换",
            font=ctk.CTkFont(family="Microsoft YaHei", size=18, weight="bold"),
            text_color=self.TEXT_PRIMARY
        )
        title_label.pack(pady=(20, 25))

        # 时间输入框容器
        input_frame = ctk.CTkFrame(self.timestamp_tab, fg_color="transparent")
        input_frame.pack(fill="x", padx=20, pady=(0, 15))

        # 输入标签 - 改进样式
        ctk.CTkLabel(
            input_frame,
            text="🕒 输入时间戳/时间:",
            font=ctk.CTkFont(family="Microsoft YaHei", size=13, weight="500"),
            text_color=self.TEXT_SECONDARY
        ).pack(anchor="w", padx=20, pady=(15, 8))

        # 时间输入框 - 使用新的颜色系统
        self.timestamp_value_var = tk.StringVar()
        self.timestamp_entry = ctk.CTkEntry(
            input_frame,
            textvariable=self.timestamp_value_var,
            placeholder_text="请输入时间戳或时间...",
            font=ctk.CTkFont(family="Microsoft YaHei", size=13),
            height=40,
            corner_radius=10,
            fg_color=self.BACKGROUND_CARD,
            border_color=self.BORDER_MEDIUM,
            border_width=2,
            text_color=self.TEXT_PRIMARY
        )
        self.timestamp_entry.pack(fill="x", padx=20, pady=(0, 20))

        # 转换类型选择容器
        type_frame = ctk.CTkFrame(self.timestamp_tab, fg_color="transparent")
        type_frame.pack(fill="x", padx=20, pady=(0, 15))

        # 转换类型标签 - 改进样式
        ctk.CTkLabel(
            type_frame,
            text="🔄 转换类型:",
            font=ctk.CTkFont(family="Microsoft YaHei", size=13, weight="500"),
            text_color=self.TEXT_SECONDARY
        ).pack(anchor="w", padx=20, pady=(15, 8))

        # 转换类型下拉框 - 使用新的颜色系统
        self.timestamp_type_var = tk.StringVar()
        self.timestamp_type_combo = ctk.CTkComboBox(
            type_frame,
            variable=self.timestamp_type_var,
            values=["时间戳转时间", "时间转时间戳"],
            font=ctk.CTkFont(family="Microsoft YaHei", size=13),
            height=40,
            state="readonly",
            corner_radius=10,
            fg_color=self.BACKGROUND_CARD,
            border_color=self.BORDER_MEDIUM,
            border_width=2,
            button_color=self.PRIMARY_BLUE,
            button_hover_color=self.HOVER_BLUE,
            dropdown_fg_color=self.BACKGROUND_CARD,
            dropdown_hover_color=self.LIGHT_BLUE,
            dropdown_text_color=self.TEXT_PRIMARY,
            text_color=self.TEXT_PRIMARY
        )
        self.timestamp_type_combo.set("时间戳转时间")
        self.timestamp_type_combo.pack(fill="x", padx=20, pady=(0, 20))

        # 按钮区域容器
        button_frame = ctk.CTkFrame(self.timestamp_tab, fg_color="transparent")
        button_frame.pack(fill="x", padx=20, pady=(0, 15))

        # 当前时间按钮 - 改进设计
        self.now_button = ctk.CTkButton(
            button_frame,
            text="📅 当前时间",
            command=self.set_current_time,
            font=ctk.CTkFont(family="Microsoft YaHei", size=13, weight="500"),
            height=40,
            fg_color=self.BACKGROUND_SECONDARY,
            hover_color=self.BORDER_MEDIUM,
            text_color=self.TEXT_SECONDARY,
            corner_radius=10
        )
        self.now_button.pack(side="left", padx=(20, 10), pady=20, fill="x", expand=True)

        # 转换按钮 - 现代化设计
        self.timestamp_convert_btn = ctk.CTkButton(
            button_frame,
            text="� 开始转换",
            command=self.convert_timestamp,
            font=ctk.CTkFont(family="Microsoft YaHei", size=14, weight="600"),
            height=45,
            fg_color=self.PRIMARY_BLUE,
            hover_color=self.HOVER_BLUE,
            text_color=self.TEXT_WHITE,
            corner_radius=12
        )
        self.timestamp_convert_btn.pack(side="right", padx=(10, 20), pady=20, fill="x", expand=True)

        # 初始化结果变量（用于右侧面板显示）
        self.timestamp_results = {
            'main': tk.StringVar(value=""),
            'detail': tk.StringVar(value=""),
            'iso': tk.StringVar(value=""),
            'relative': tk.StringVar(value="")
        }

        # 绑定事件
        self.timestamp_entry.bind("<Return>", lambda e: self.convert_timestamp())
        self.timestamp_entry.bind("<KeyRelease>", lambda e: self.validate_timestamp_input())
        self.timestamp_type_combo.configure(command=lambda choice: self.update_timestamp_placeholder())
        self.update_timestamp_placeholder()

    def validate_byte_input(self):
        """Validate that the input is a valid number for byte conversion"""
        value = self.byte_value_var.get().strip()
        if not value:
            return

        try:
            # Allow for decimal numbers
            float(value)
            self.status_var.set("准备就绪")
        except ValueError:
            self.status_var.set("请输入有效的数字")

    def validate_timestamp_input(self):
        """Validate that the input is a valid timestamp"""
        value = self.timestamp_value_var.get().strip()
        if not value:
            return

        try:
            # Allow for integer or decimal numbers
            float(value)
            self.status_var.set("准备就绪")
        except ValueError:
            self.status_var.set("请输入有效的时间戳")

    def format_bytes_value(self, value):
        """Format byte values for better display"""
        if value == 0:
            return "0"

        # 对于非常大的数字使用科学计数法
        if abs(value) >= 1e15:
            return f"{value:.2e}"

        # 对于非常小的数字使用科学计数法
        if abs(value) < 1e-6 and value != 0:
            return f"{value:.2e}"

        # 对于整数，直接显示
        if value == int(value):
            return f"{int(value):,}"

        # 对于小数，根据大小确定精度
        if abs(value) >= 1000:
            return f"{value:,.2f}"
        elif abs(value) >= 1:
            return f"{value:.4f}"
        else:
            return f"{value:.6f}".rstrip('0').rstrip('.')

    def get_relative_time(self, timestamp):
        """Get relative time description"""
        try:
            now = time.time()
            diff = now - timestamp

            if abs(diff) < 60:
                return "刚刚"
            elif abs(diff) < 3600:
                minutes = int(abs(diff) / 60)
                return f"{minutes}分钟{'前' if diff > 0 else '后'}"
            elif abs(diff) < 86400:
                hours = int(abs(diff) / 3600)
                return f"{hours}小时{'前' if diff > 0 else '后'}"
            elif abs(diff) < 2592000:  # 30 days
                days = int(abs(diff) / 86400)
                return f"{days}天{'前' if diff > 0 else '后'}"
            elif abs(diff) < 31536000:  # 365 days
                months = int(abs(diff) / 2592000)
                return f"{months}个月{'前' if diff > 0 else '后'}"
            else:
                years = int(abs(diff) / 31536000)
                return f"{years}年{'前' if diff > 0 else '后'}"
        except:
            return "无法计算"

    def convert_bytes(self):
        """Convert the input value to all byte units"""
        value = self.byte_value_var.get().strip()
        if not value:
            return

        try:
            # Get the numeric value and source unit
            numeric_value = float(value)
            source_unit = self.byte_unit_var.get()
            source_power = self.units[source_unit]

            # Convert to bytes first (base unit)
            bytes_value = numeric_value * (1024 ** source_power)

            # 构建结果文本
            result_text = f"📊 字节单位转换结果\n\n"
            result_text += f"输入: {self.format_bytes_value(numeric_value)} {source_unit}\n\n"
            result_text += "转换结果:\n"
            result_text += "=" * 40 + "\n"

            # Convert to all units and format for display
            for unit, power in self.units.items():
                result = bytes_value / (1024 ** power)
                formatted_result = self.format_bytes_value(result)

                # 高亮显示源单位
                if unit == source_unit:
                    result_text += f"➤ {unit}: {formatted_result} ⭐\n"
                else:
                    result_text += f"  {unit}: {formatted_result}\n"

            # 添加额外信息
            result_text += "\n" + "=" * 40 + "\n"
            result_text += f"基础字节数: {self.format_bytes_value(bytes_value)} Bytes\n"

            # 更新右侧结果展示面板
            self.result_display.delete("1.0", "end")
            self.result_display.insert("1.0", result_text)

            self.status_var.set(f"✅ 已将 {self.format_bytes_value(numeric_value)} {source_unit} 转换为所有单位")

        except ValueError:
            self.status_var.set("❌ 请输入有效的数字")
        except Exception as e:
            self.status_var.set(f"❌ 转换错误: {str(e)}")

    def convert_timestamp(self):
        """Convert the timestamp to human-readable date and time or vice versa"""
        value = self.timestamp_value_var.get().strip()
        if not value:
            return

        try:
            # 获取转换类型
            conversion_type = self.timestamp_type_var.get()

            if conversion_type == "时间戳转时间":
                # 时间戳转时间
                timestamp_value = float(value)

                # 转换为datetime对象
                dt = datetime.datetime.fromtimestamp(timestamp_value)

                # 主要结果 - 标准格式
                main_result = dt.strftime("%Y-%m-%d %H:%M:%S")

                # 详细信息 - 中文格式和星期
                weekdays = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"]
                weekday = weekdays[dt.weekday()]
                detail_result = f"{dt.year}年{dt.month:02d}月{dt.day:02d}日 {weekday} {dt.hour:02d}时{dt.minute:02d}分{dt.second:02d}秒"

                # ISO格式
                iso_result = dt.isoformat()

                # 相对时间
                relative_result = self.get_relative_time(timestamp_value)

                # 更新状态栏
                self.status_var.set(f"已将时间戳 {value} 转换为日期时间")

            else:
                # 时间转时间戳
                try:
                    # 尝试解析时间字符串
                    dt = datetime.datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    try:
                        # 尝试其他常见格式
                        dt = datetime.datetime.strptime(value, "%Y/%m/%d %H:%M:%S")
                    except ValueError:
                        try:
                            dt = datetime.datetime.strptime(value, "%Y-%m-%d")
                        except ValueError:
                            raise ValueError("无法解析时间格式，请使用 YYYY-MM-DD HH:MM:SS 格式")

                # 获取时间戳
                timestamp = dt.timestamp()

                # 主要结果 - Unix时间戳
                main_result = f"Unix时间戳: {int(timestamp)} 秒"

                # 详细信息 - 毫秒时间戳
                detail_result = f"毫秒时间戳: {int(timestamp * 1000)} 毫秒"

                # ISO格式
                iso_result = dt.isoformat()

                # 相对时间
                relative_result = self.get_relative_time(timestamp)

                # 更新状态栏
                self.status_var.set(f"已将日期时间 {value} 转换为时间戳")

            # 构建结果文本
            result_text = f"⏰ 时间戳转换结果\n\n"
            result_text += f"输入: {value}\n"
            result_text += f"转换类型: {conversion_type}\n\n"
            result_text += "转换结果:\n"
            result_text += "=" * 40 + "\n"
            result_text += f"📅 主要结果:\n{main_result}\n\n"
            result_text += f"📝 详细信息:\n{detail_result}\n\n"
            result_text += f"🌐 ISO格式:\n{iso_result}\n\n"
            result_text += f"⏱️ 相对时间:\n{relative_result}\n"

            # 更新右侧结果展示面板
            self.result_display.delete("1.0", "end")
            self.result_display.insert("1.0", result_text)

            # 更新结果变量（保持兼容性）
            self.timestamp_results['main'].set(main_result)
            self.timestamp_results['detail'].set(detail_result)
            self.timestamp_results['iso'].set(iso_result)
            self.timestamp_results['relative'].set(relative_result)

        except ValueError as e:
            if "无法解析时间格式" in str(e):
                self.status_var.set(f"❌ {str(e)}")
            else:
                self.status_var.set("❌ 请输入有效的时间戳或时间格式")
        except Exception as e:
            self.status_var.set(f"❌ 转换错误: {str(e)}")

    def update_timestamp_placeholder(self):
        """Update the placeholder text based on the selected conversion type"""
        conversion_type = self.timestamp_type_var.get()
        if conversion_type == "时间戳转时间":
            # 清除当前输入
            self.timestamp_entry.delete(0, tk.END)
            # 清除所有结果
            for key in self.timestamp_results:
                self.timestamp_results[key].set("")
            # 设置状态栏文本
            if hasattr(self, 'status_var'):
                self.status_var.set("请输入Unix时间戳值，例如: 1609459200")
        else:
            # 清除当前输入
            self.timestamp_entry.delete(0, tk.END)
            # 清除所有结果
            for key in self.timestamp_results:
                self.timestamp_results[key].set("")
            # 设置状态栏文本
            if hasattr(self, 'status_var'):
                self.status_var.set("请输入时间，例如: 2021-01-01 00:00:00")

    def set_current_time(self):
        """Set current time or timestamp based on the conversion type"""
        # 获取转换类型
        conversion_type = self.timestamp_type_var.get()

        if conversion_type == "时间转时间戳":
            # 如果是时间转时间戳，设置当前时间
            now = datetime.datetime.now()
            self.timestamp_value_var.set(now.strftime("%Y-%m-%d %H:%M:%S"))
        else:
            # 如果是时间戳转时间，设置当前时间戳
            current_timestamp = time.time()
            self.timestamp_value_var.set(str(int(current_timestamp)))

        # 转换
        self.convert_timestamp()

    def setup_currency_converter_ui(self):
        """设置货币转换UI - 使用现代化设计"""
        # 输入区域标题 - 改进样式
        title_label = ctk.CTkLabel(
            self.currency_tab,
            text="💰 货币汇率转换",
            font=ctk.CTkFont(family="Microsoft YaHei", size=18, weight="bold"),
            text_color=self.TEXT_PRIMARY
        )
        title_label.pack(pady=(20, 25))

        # 美元金额输入框容器
        usd_frame = ctk.CTkFrame(self.currency_tab, fg_color="transparent")
        usd_frame.pack(fill="x", padx=20, pady=(0, 15))

        # 美元金额标签 - 改进样式
        ctk.CTkLabel(
            usd_frame,
            text="💵 输入美元金额:",
            font=ctk.CTkFont(family="Microsoft YaHei", size=13, weight="500"),
            text_color=self.TEXT_SECONDARY
        ).pack(anchor="w", padx=20, pady=(15, 8))

        # 美元金额输入框 - 使用新的颜色系统
        self.usd_value_var = tk.StringVar()
        self.usd_entry = ctk.CTkEntry(
            usd_frame,
            textvariable=self.usd_value_var,
            placeholder_text="请输入美元金额...",
            font=ctk.CTkFont(family="Microsoft YaHei", size=13),
            height=40,
            corner_radius=10,
            fg_color=self.BACKGROUND_CARD,
            border_color=self.BORDER_MEDIUM,
            border_width=2,
            text_color=self.TEXT_PRIMARY
        )
        self.usd_entry.pack(fill="x", padx=20, pady=(0, 20))

        # 汇率设置区域容器
        rate_frame = ctk.CTkFrame(self.currency_tab, fg_color="transparent")
        rate_frame.pack(fill="x", padx=20, pady=(0, 15))

        # 汇率标签 - 改进样式
        ctk.CTkLabel(
            rate_frame,
            text="💱 当前汇率 (人民币/美元):",
            font=ctk.CTkFont(family="Microsoft YaHei", size=13, weight="500"),
            text_color=self.TEXT_SECONDARY
        ).pack(anchor="w", padx=20, pady=(15, 8))

        # 汇率输入框 - 使用新的颜色系统
        self.rate_var = tk.StringVar(value=str(self.exchange_rate))
        self.rate_entry = ctk.CTkEntry(
            rate_frame,
            textvariable=self.rate_var,
            font=ctk.CTkFont(family="Microsoft YaHei", size=13),
            height=40,
            corner_radius=10,
            fg_color=self.BACKGROUND_CARD,
            border_color=self.BORDER_MEDIUM,
            border_width=2,
            text_color=self.TEXT_PRIMARY
        )
        self.rate_entry.pack(fill="x", padx=20, pady=(0, 12))

        # 更新时间显示 - 改进样式
        self.update_time_var = tk.StringVar(value="🕒 尚未更新汇率")
        self.update_time_label = ctk.CTkLabel(
            rate_frame,
            textvariable=self.update_time_var,
            font=ctk.CTkFont(family="Microsoft YaHei", size=11),
            text_color=self.TEXT_MUTED
        )
        self.update_time_label.pack(anchor="w", padx=20, pady=(0, 20))

        # 按钮区域容器
        button_frame = ctk.CTkFrame(self.currency_tab, fg_color="transparent")
        button_frame.pack(fill="x", padx=20, pady=(0, 15))

        # 更新汇率按钮 - 改进设计
        self.update_rate_btn = ctk.CTkButton(
            button_frame,
            text="🔄 更新汇率",
            command=self.get_exchange_rate,
            font=ctk.CTkFont(family="Microsoft YaHei", size=13, weight="500"),
            height=40,
            fg_color=self.BACKGROUND_SECONDARY,
            hover_color=self.BORDER_MEDIUM,
            text_color=self.TEXT_SECONDARY,
            corner_radius=10
        )
        self.update_rate_btn.pack(side="left", padx=(20, 10), pady=20, fill="x", expand=True)

        # 转换按钮 - 现代化设计
        self.currency_convert_btn = ctk.CTkButton(
            button_frame,
            text="� 开始转换",
            command=self.convert_currency,
            font=ctk.CTkFont(family="Microsoft YaHei", size=14, weight="600"),
            height=45,
            fg_color=self.PRIMARY_BLUE,
            hover_color=self.HOVER_BLUE,
            text_color=self.TEXT_WHITE,
            corner_radius=12
        )
        self.currency_convert_btn.pack(side="right", padx=(10, 20), pady=20, fill="x", expand=True)

        # 绑定事件
        self.usd_entry.bind("<Return>", lambda e: self.convert_currency())
        self.usd_entry.bind("<KeyRelease>", lambda e: self.validate_currency_input())
        self.rate_entry.bind("<KeyRelease>", lambda e: self.update_manual_rate())

        # 初始化结果变量（用于右侧面板显示）
        self.usd_result_var = tk.StringVar(value="0.00")
        self.cny_result_var = tk.StringVar(value="0.00")

    def validate_currency_input(self):
        """验证货币输入是否为有效数字"""
        value = self.usd_value_var.get().strip()
        if not value:
            return

        try:
            # 允许小数输入
            float(value)
            self.status_var.set("准备就绪")
        except ValueError:
            self.status_var.set("请输入有效的数字")
            
    def update_manual_rate(self):
        """更新手动设置的汇率"""
        try:
            rate = float(self.rate_var.get())
            if rate <= 0:
                self.status_var.set("汇率必须大于0")
                return
                
            self.exchange_rate = rate
            self.status_var.set(f"已手动更新汇率: 1美元 = {rate}人民币")
            self.update_time_var.set("手动设置")
            # 如果已有输入，自动转换
            if self.usd_value_var.get().strip():
                self.convert_currency()
        except ValueError:
            self.status_var.set("请输入有效的汇率数字")
            
    def get_exchange_rate(self):
        """获取实时汇率数据（从中国银行官网获取）"""
        self.status_var.set("正在获取最新汇率...")
        
        # 检查是否安装了BeautifulSoup
        if not BS4_AVAILABLE:
            self.status_var.set("未安装BeautifulSoup库，无法从中国银行网站获取汇率。使用备用数据源...")
            self.get_exchange_rate_backup()
            return
            
        try:
            # 使用中国银行官网获取美元兑人民币汇率
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            # 中国银行外汇牌价网址
            url = "https://www.boc.cn/sourcedb/whpj/enindex_1619.html"
            
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                # 使用BeautifulSoup解析HTML
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 找到汇率表格
                table = soup.find('table', {'bgcolor': '#EAEAEA'})
                if table:
                    # 查找所有行
                    rows = table.find_all('tr')
                    
                    # 遍历行寻找USD(美元)汇率
                    for row in rows[1:]:  # 跳过表头行
                        cells = row.find_all('td')
                        if len(cells) >= 7:  # 确保行有足够的单元格
                            currency_name = cells[0].text.strip()
                            if currency_name == 'USD':
                                # 获取卖出价作为汇率
                                selling_rate_text = cells[3].text.strip()
                                if selling_rate_text and selling_rate_text != '--':
                                    try:
                                        selling_rate = float(selling_rate_text)
                                        # 中行牌价是按照100外币单位显示的，需要除以100
                                        self.exchange_rate = selling_rate / 100
                                        self.rate_var.set(str(round(self.exchange_rate, 4)))
                                        self.last_rate_update = datetime.datetime.now()
                                        
                                        # 更新状态和时间显示
                                        self.status_var.set(f"汇率更新成功: 1美元 = {self.exchange_rate}人民币")
                                        self.update_time_var.set(f"更新于: {self.last_rate_update.strftime('%Y-%m-%d %H:%M')}")
                                        
                                        # 如果已有输入，自动转换
                                        if self.usd_value_var.get().strip():
                                            self.convert_currency()
                                        return
                                    except ValueError:
                                        self.status_var.set(f"解析汇率数据失败，数值格式错误: {selling_rate_text}")
                
                # 尝试使用中间价(Middle Rate)
                table = soup.find('table', {'bgcolor': '#EAEAEA'})
                if table:
                    for row in table.find_all('tr')[1:]:
                        cells = row.find_all('td')
                        if len(cells) >= 7:
                            currency_name = cells[0].text.strip()
                            if currency_name == 'USD':
                                middle_rate_text = cells[5].text.strip()
                                if middle_rate_text and middle_rate_text != '--':
                                    try:
                                        middle_rate = float(middle_rate_text)
                                        self.exchange_rate = middle_rate / 100
                                        self.rate_var.set(str(round(self.exchange_rate, 4)))
                                        self.last_rate_update = datetime.datetime.now()
                                        
                                        self.status_var.set(f"汇率更新成功(使用中间价): 1美元 = {self.exchange_rate}人民币")
                                        self.update_time_var.set(f"更新于: {self.last_rate_update.strftime('%Y-%m-%d %H:%M')}")
                                        
                                        if self.usd_value_var.get().strip():
                                            self.convert_currency()
                                        return
                                    except ValueError:
                                        pass
                
                # 如果未找到USD汇率或解析失败，尝试备用方法
                self.status_var.set("未在中国银行网站找到美元汇率，使用备用数据源...")
                self.get_exchange_rate_backup()
            else:
                # 如果请求失败，尝试备用方法
                self.status_var.set("无法连接到中国银行网站，使用备用数据源...")
                self.get_exchange_rate_backup()
        except Exception as e:
            self.status_var.set(f"获取汇率失败: {str(e)}")
            # 尝试备用方法
            self.get_exchange_rate_backup()
            
    def get_exchange_rate_backup(self):
        """备用方法获取汇率（使用开放API）"""
        try:
            # 首先尝试使用第一个备用API
            response = requests.get("https://open.er-api.com/v6/latest/USD", timeout=10)
            data = response.json()
            
            if response.status_code == 200 and "rates" in data and "CNY" in data["rates"]:
                self.exchange_rate = data["rates"]["CNY"]
                self.rate_var.set(str(round(self.exchange_rate, 4)))
                self.last_rate_update = datetime.datetime.now()
                
                # 更新状态和时间显示
                self.status_var.set(f"汇率更新成功(备用源1): 1美元 = {self.exchange_rate}人民币")
                self.update_time_var.set(f"更新于: {self.last_rate_update.strftime('%Y-%m-%d %H:%M')}")
                
                # 如果已有输入，自动转换
                if self.usd_value_var.get().strip():
                    self.convert_currency()
                return
                
            # 如果第一个备用API失败，尝试第二个备用API
            self.status_var.set("正在尝试第二个备用数据源...")
            response = requests.get("https://api.exchangerate-api.com/v4/latest/USD", timeout=10)
            data = response.json()
            
            if response.status_code == 200 and "rates" in data and "CNY" in data["rates"]:
                self.exchange_rate = data["rates"]["CNY"]
                self.rate_var.set(str(round(self.exchange_rate, 4)))
                self.last_rate_update = datetime.datetime.now()
                
                # 更新状态和时间显示
                self.status_var.set(f"汇率更新成功(备用源2): 1美元 = {self.exchange_rate}人民币")
                self.update_time_var.set(f"更新于: {self.last_rate_update.strftime('%Y-%m-%d %H:%M')}")
                
                # 如果已有输入，自动转换
                if self.usd_value_var.get().strip():
                    self.convert_currency()
                return
            
            # 所有API都失败，提示用户手动设置
            self.status_var.set("无法获取汇率数据，请手动设置汇率或稍后再试")
        except Exception as e:
            self.status_var.set(f"获取汇率失败: {str(e)}，请手动设置汇率")
            
    def convert_currency(self):
        """将美元转换为人民币"""
        value = self.usd_value_var.get().strip()
        if not value:
            return
            
        try:
            # 获取美元金额
            usd_amount = float(value)
            
            # 转换为人民币
            cny_amount = usd_amount * self.exchange_rate
            
            # 格式化显示
            usd_formatted = f"${usd_amount:,.2f}"
            cny_formatted = f"¥{cny_amount:,.2f}"

            # 构建结果文本
            result_text = f"💰 货币汇率转换结果\n\n"
            result_text += f"输入金额: {usd_formatted} USD\n"
            result_text += f"当前汇率: {self.exchange_rate:.4f} CNY/USD\n\n"
            result_text += "转换结果:\n"
            result_text += "=" * 40 + "\n"
            result_text += f"💵 美元金额: {usd_formatted}\n"
            result_text += f"💴 人民币金额: {cny_formatted}\n\n"
            result_text += "计算详情:\n"
            result_text += "=" * 40 + "\n"
            result_text += f"计算公式: {usd_amount} × {self.exchange_rate:.4f} = {cny_amount:.2f}\n"
            result_text += f"汇率更新时间: {self.update_time_var.get()}\n"

            # 更新右侧结果展示面板
            self.result_display.delete("1.0", "end")
            self.result_display.insert("1.0", result_text)

            # 更新结果变量（保持兼容性）
            self.usd_result_var.set(usd_formatted)
            self.cny_result_var.set(cny_formatted)

            # 更新状态栏
            self.status_var.set(f"✅ 已将 {usd_formatted} 转换为 {cny_formatted}")
            
        except ValueError:
            self.status_var.set("❌ 请输入有效的数字")
        except Exception as e:
            self.status_var.set(f"❌ 转换错误: {str(e)}")

    def run(self):
        """运行应用程序"""
        self.root.mainloop()

def main():
    """主函数"""
    app = ConverterApp()
    app.run()

if __name__ == "__main__":
    main()
